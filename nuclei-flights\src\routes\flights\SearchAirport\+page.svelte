<script lang="ts">
  import { page } from "$app/stores";
  import { base } from "$app/paths";
  import { onMount, onDestroy } from "svelte";
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { ApiUtil } from "@CDNA-Technologies/svelte-vitals/api-util";
  import type {
    AirportSuggestionsResponse,
    AirportList,
    AirportSearchRequest,
  } from "$lib/flights-commons/messages/airport-search.msg.ts";
  import {
    fetchPopularCities,
    type Airport,
  } from "$lib/flights-commons/utils/city-search-api.util.js"; // Import the new service file
  import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
  import { getAirportSearchResults } from "$flights/flight-api.js";

  // Import the template component
  import AirportSearchCard from "./components/AirportSearchCard.svelte";

  let searchQuery = "";
  let recentSearches = [];
  let popularCities: Airport[] = []; // This will be populated from API
  let isLoadingPopular = true;
  let isSearching = false;
  let suggestedResponse: AirportSuggestionsResponse | undefined;
  let searchTimeout: NodeJS.Timeout;

  // Define Airport type to match your existing structure
  const allAirports: Airport[] = [
    {
      code: "BLR",
      name: "Bangalore",
      airport: "Kempegowda International Airport",
    },
  ];

  // Debounced search function
  async function performSearch(query: string) {
    if (query.length > 2) {
      isSearching = true;
      try {
        let apiResponse = await getAirportSearchResults(query);
        if (apiResponse.hasError()) {
          suggestedResponse = undefined;
        } else {
          suggestedResponse = apiResponse.response!;
        }
      } catch (error) {
        suggestedResponse = undefined;
      }
      isSearching = false;
    } else {
      suggestedResponse = undefined;
    }
  }

  // Handle search input with debouncing
  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Reset suggestions if query is too short
    if (searchQuery.length <= 2) {
      suggestedResponse = undefined;
      isSearching = false;
      return;
    }

    // Set new timeout for debounced search
    searchTimeout = setTimeout(() => {
      performSearch(searchQuery);
    }, 300); // 300ms debounce
  }

  // Get the selectionType from the URL state passed from the previous page
  $: isSourceSelection = $page.url.searchParams.has("source");
  $: isDestinationSelection = $page.url.searchParams.has("destination");
  $: selectionType = isSourceSelection
    ? "Source"
    : isDestinationSelection
    ? "Destination"
    : "Source";
  $: heading = `Search ${selectionType} City`;

  // Filter airports based on the search query
  $: filteredAirports = allAirports.filter(
    (airport) =>
      airport.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      airport.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      airport.airport.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Convert API airport format to local Airport format
  function convertApiAirportToLocal(apiAirport: AirportList): Airport {
    return {
      code: apiAirport.iataCode,
      name: apiAirport.city,
      airport: apiAirport.name,
    };
  }

  // Get airports to display based on search state
  $: airportsToShow = (() => {
    if (!searchQuery) return [];

    // If we have API results, use them
    if (suggestedResponse?.airportList) {
      return suggestedResponse.airportList.map(convertApiAirportToLocal);
    }

    // Fallback to local filtered results
    return filteredAirports.slice(0, 10);
  })();

  // Get current selected cities from navigation state
  let selectedCities: any;

  // Error state for duplicate city selection
  let showDuplicateError = false;
  let duplicateErrorMessage = "";

  // Filter popular cities to exclude already selected cities
  $: filteredPopularCities = (() => {
    if (!popularCities.length || !selectedCities) {
      NucleiLogger.logInfo(
        "No filtering needed - returning all popular cities",
        "SEARCHAIRPORT",
        {
          popularCitiesLength: popularCities.length,
          hasSelectedCities: !!selectedCities,
        }
      );
      return popularCities;
    }

    // Get the city to exclude based on selection type
    const excludeCity = isSourceSelection
      ? selectedCities.to
      : selectedCities.from;

    // Filter out the already selected city
    const filtered = popularCities.filter(
      (city) =>
        city.code !== excludeCity?.code && city.name !== excludeCity?.city
    );

    NucleiLogger.logInfo("Filtered popular cities", "SEARCHAIRPORT", {
      filteredCount: filtered.length,
      excludedCity: excludeCity,
    });

    return filtered;
  })();

  // Load popular cities on component mount
  onMount(async () => {
    // Access navigation state from history
    selectedCities = history.state?.selectedCities;
    NucleiLogger.logInfo(
      "Selected cities from navigation state:",
      "SEARCHAIRPORT",
      selectedCities
    );

    isLoadingPopular = true;

    try {
      // Use the API service function to get data
      popularCities = await fetchPopularCities();
      NucleiLogger.logInfo(
        "Popular cities loaded:",
        "SEARCHAIRPORT",
        popularCities
      );
    } catch (error) {
      NucleiLogger.logWarn(
        "Error loading popular cities:",
        "SEARCHAIRPORT",
        error
      );
    } finally {
      isLoadingPopular = false;
    }
  });

  // Cleanup timeout on component destroy
  onDestroy(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
  });

  // Function to handle when a city is clicked
  function handleCityClick(city: Airport) {
    // Check if user is trying to select the same city for source and destination
    if (selectedCities) {
      const excludeCity = isSourceSelection
        ? selectedCities.to
        : selectedCities.from;

      if (
        excludeCity &&
        (city.code === excludeCity.code || city.name === excludeCity.city)
      ) {
        // Show error instead of navigating
        showDuplicateError = true;
        duplicateErrorMessage = "Source and destination cannot be same";

        NucleiLogger.logWarn(
          "SEARCHAIRPORT",
          `Duplicate city selection attempted: ${city.name}`
        );
        return; // Don't navigate
      }
    }

    // Clear any existing error
    showDuplicateError = false;

    NucleiLogger.logInfo(
      "SEARCHAIRPORT",
      `Selected city: ${city.name} for ${selectionType}`
    );
    NavigatorUtils.navigateTo({
      url: base + "/flights/landing",
      opts: {
        replaceState: false,
        state: {
          selectionType: selectionType,
          city: { city: city.name, airport: city.airport, code: city.code },
        },
      },
    });
  }
</script>

<AirportSearchCard
  {heading}
  {searchQuery}
  {isSearching}
  {airportsToShow}
  {recentSearches}
  popularCities={filteredPopularCities}
  {isLoadingPopular}
  {handleSearchInput}
  {handleCityClick}
  {selectedCities}
  {isSourceSelection}
  {showDuplicateError}
  {duplicateErrorMessage}
/>
