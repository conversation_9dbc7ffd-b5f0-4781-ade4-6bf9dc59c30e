<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let dateOptions: { day: string; price: number }[] = [];
	export let selectedDateIndex: number = 1;
	export let showRefundableOnly: boolean = true;

	const dispatch = createEventDispatcher();

	function handleDateSelect(index: number) {
		dispatch('dateSelect', { index });
	}

	function handleSortFilter() {
		dispatch('sortFilter');
	}

	function toggleRefundable() {
		dispatch('toggleRefundable');
	}
</script>

<!-- Date Selector -->
<div class="bg-white px-4 py-3 border-b border-gray-200">
	<div class="flex space-x-2 overflow-x-auto">
		{#each dateOptions as dateOption, index}
			<button
				class="flex-shrink-0 px-3 py-2 rounded-lg text-center {selectedDateIndex === index
					? 'bg-blue-100 text-blue-700'
					: 'bg-gray-100 text-gray-700'}"
				on:click={() => handleDateSelect(index)}
			>
				<div class="text-xs font-medium">{dateOption.day}</div>
				<div class="text-sm font-bold">₹ {dateOption.price}</div>
			</button>
		{/each}
	</div>
</div>

<!-- Filter Controls -->
<div class="bg-white px-4 py-3 border-b border-gray-200">
	<div class="flex items-center space-x-3 overflow-x-auto pb-2 scrollbar-hide">
		<button
			class="select-none flex items-center px-4 py-2 bg-blue-600 text-white rounded-full text-sm font-medium whitespace-nowrap"
			on:click={handleSortFilter}
		>
			<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
				/>
			</svg>
			Sort & Filter
		</button>

		<button
			class="select-none px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium whitespace-nowrap"
		>
			Cheapest
		</button>

		<button
			class="select-none px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium whitespace-nowrap"
		>
			Non-Stop
		</button>

		<button
			class="select-none px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium whitespace-nowrap"
		>
			Morning-Departure
		</button>

		<button
			class="select-none px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium whitespace-nowrap"
		>
			Late-Departure
		</button>

		<button
			class="select-none flex items-center px-4 py-2 {showRefundableOnly
				? 'bg-blue-100 text-blue-700'
				: 'bg-gray-100 text-gray-700'} rounded-full text-sm font-medium whitespace-nowrap"
			on:click={toggleRefundable}
		>
			Refundable
			{#if showRefundableOnly}
				<svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
						clip-rule="evenodd"
					/>
				</svg>
			{/if}
		</button>
	</div>
</div>
