<script lang="ts">
  // Import required modules
  import { base } from "$app/paths";
  import { ListCouponCta } from "@CDNA-Technologies/svelte-vitals/cart/coupons";
  import { LandingRewardCta } from "@CDNA-Technologies/svelte-vitals/cart/rewards";
  import { LandingWalletCta } from "@CDNA-Technologies/svelte-vitals/cart/wallet";
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
  import ThreeDotMenu from "@CDNA-Technologies/svelte-vitals/components/three-dot-menu";

  import {
    ErrorHandling,
    lceStore,
    setContentLce,
    setLoadingLce,
  } from "@CDNA-Technologies/svelte-vitals/error-handling";
  import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  import {
    clearBottomSheetFromStore,
    isAnyBottomSheetPresent,
  } from "@CDNA-Technologies/svelte-vitals/components/bottom-sheet";
  // Fixed imports based on your file structure
  import FlightSearchForm from "./FlightSearchForm.svelte";
  import { cityUtils } from "$lib/flights-commons/utils/city-util";
  import { dateUtils } from "$lib/flights-commons/utils/date-util";

  // Initialize with stored data instead of just cities
  let flightData = cityUtils.getInitialFlightData();
  let selectedCities = flightData.cities;
  let searchParams = flightData.searchParams;

  // Function to handle query parameters and override specific search params
  function handleQueryParams() {
    if (typeof window !== "undefined" && $page.url.searchParams) {
      const urlParams = $page.url.searchParams;

      // Check if we have query parameters for departure, class, or passengers
      const departure = urlParams.get("departure");
      const classParam = urlParams.get("class");
      const passengers = urlParams.get("passengers");

      if (departure || classParam || passengers) {
        NucleiLogger.logInfo(
          "LANDING",
          "Found query parameters, overriding search params:",
          {
            departure,
            class: classParam,
            passengers,
          }
        );

        // Override specific search params with query parameters
        const updatedSearchParams = { ...searchParams };

        if (departure) {
          updatedSearchParams.departureDate = departure;
          // Try to format the day if possible
          try {
            const date = new Date(departure);
            if (!isNaN(date.getTime())) {
              updatedSearchParams.departureDay = dateUtils.formatDay(date);
            }
          } catch (e) {
            // If date parsing fails, keep the departure as is
            updatedSearchParams.departureDay = "Monday"; // fallback
          }
        }

        if (classParam) {
          updatedSearchParams.selectedClass = classParam;
        }

        if (passengers) {
          const passengerCount = parseInt(passengers);
          if (!isNaN(passengerCount)) {
            updatedSearchParams.travellers = passengerCount
              .toString()
              .padStart(2, "0");
            updatedSearchParams.adults = passengerCount; // Assuming all are adults for now
            updatedSearchParams.children = 0;
            updatedSearchParams.infants = 0;
          }
        }

        // Update the searchParams
        searchParams = updatedSearchParams;

        // Save the updated data (keeping cities from localStorage, updating search params from URL)
        cityUtils.saveFlightSearchData(selectedCities, searchParams);

        // Clear the query parameters from URL to prevent them from persisting
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete("departure");
        newUrl.searchParams.delete("class");
        newUrl.searchParams.delete("passengers");
        window.history.replaceState({}, "", newUrl.toString());
      }
    }
  }

  // Reactive statement to handle query parameters when page changes
  $: if ($page.url.searchParams) {
    handleQueryParams();
  }

  function handleNavigationState() {
    // Get the navigation state
    const state = history.state;
    NucleiLogger.logInfo("Navigation state:", "LANDING", state);

    if (state && state.selectionType && state.city) {
      NucleiLogger.logDebug("Processing navigation state:", "LANDING", state);

      const { selectionType, city } = state;

      // Update the appropriate city based on selection type
      if (selectionType === "Source") {
        selectedCities.from = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
      } else if (selectionType === "Destination") {
        selectedCities.to = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
      }

      // Trigger reactivity by reassigning the object
      selectedCities = { ...selectedCities };

      // Save the updated cities while preserving search params
      cityUtils.saveFlightSearchData(selectedCities, searchParams);

      // Clear the state to prevent reprocessing on refresh
      history.replaceState(null, "", window.location.href);
    }
  }

  function handleCitySwap(event: any) {
    selectedCities = event.detail;
    // Save cities while preserving search params
    cityUtils.saveFlightSearchData(selectedCities, searchParams);
  }

  // New function to handle search params updates
  function handleSearchParamsUpdate(event: any) {
    searchParams = { ...searchParams, ...event.detail };
    // Save search params while preserving cities
    cityUtils.saveFlightSearchData(selectedCities, searchParams);
  }

  function handleSearchFlights(event: any) {
    const searchData = event.detail;

    // Clean up any open bottom sheets before navigation
    if (isAnyBottomSheetPresent()) {
      NucleiLogger.logInfo(
        "LANDING",
        "Cleaning up bottom sheets before navigation"
      );
      clearBottomSheetFromStore();
    }

    // Update search params before navigation
    searchParams = {
      ...searchParams,
      departureDate: searchData.departure,
      selectedClass: searchData.clas,
      travellers: searchData.passengers.toString().padStart(2, "0"),
      adults: searchData.adults,
      children: searchData.children,
      infants: searchData.infants,
    };

    // Save complete data before navigation
    cityUtils.saveFlightSearchData(selectedCities, searchParams);

    // Build query parameters
    const params = new URLSearchParams({
      fromCity: selectedCities.from.city,
      fromCode: selectedCities.from.code,
      fromAirport: selectedCities.from.airport,
      toCity: selectedCities.to.city,
      toCode: selectedCities.to.code,
      toAirport: selectedCities.to.airport,
      departure: searchData.departure,
      passengers: searchData.passengers.toString(),
      class: searchData.clas,
    });

    const finalUrl = `/flights-base/dev/flights/listing?${params.toString()}`;
    NucleiLogger.logInfo("LANDING", "Navigating to URL: ", finalUrl);

    NavigatorUtils.navigateTo({
      url: finalUrl,
    });
  }

  onMount(async () => {
    NucleiLogger.logInfo("LANDING", "Landing screen mounted");

    // Clean up any stale bottom sheet state when landing page loads
    // This prevents issues when returning from listing page
    if (isAnyBottomSheetPresent()) {
      NucleiLogger.logInfo("LANDING", "Cleaning up stale bottom sheet state");
      clearBottomSheetFromStore();

      // Clean up URL parameters as well
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.delete("view");
        window.history.replaceState({}, "", url.toString());
      }
    }

    setLoadingLce();
    handleNavigationState();
    await fetchScreenData();

    if (typeof window !== "undefined") {
      window.addEventListener("popstate", handleNavigationState);
    }
  });

  const fetchScreenData = async () => {
    setContentLce();
  };

  function handleRetry() {
    setLoadingLce();
    fetchScreenData();
  }
</script>

<AppBar title="Flights">
  <div slot="action">
    <div class="flex flex-row items-center gap-2">
      <LandingWalletCta />
      <ListCouponCta />
      <LandingRewardCta />
      <div class="dropdown dropdown-end"><ThreeDotMenu /></div>
    </div>
  </div>
</AppBar>

{#if $lceStore.isLoading}
  <div class="h-screen flex flex-col justify-center">
    <PrimaryLoader />
  </div>
{:else if $lceStore.hasError && $lceStore.errorDetails != null}
  <ErrorHandling
    errorHandling={$lceStore.errorDetails}
    on:submit={handleRetry}
  />
{:else if $lceStore.hasContent}
  <div class="flex-1 overflow-y-auto">
    <FlightSearchForm
      {selectedCities}
      {searchParams}
      on:citySwap={handleCitySwap}
      on:searchFlights={handleSearchFlights}
      on:searchParamsUpdate={handleSearchParamsUpdate}
    />
  </div>
{/if}
