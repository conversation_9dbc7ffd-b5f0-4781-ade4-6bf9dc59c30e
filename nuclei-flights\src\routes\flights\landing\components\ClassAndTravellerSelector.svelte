<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import { openBottomSheet } from "@CDNA-Technologies/svelte-vitals/components/bottom-sheet";
  import Icon from "../../assets/icons/Icon.svelte";

  export let selectedClass;
  export let travellers;

  const dispatch = createEventDispatcher();

  function openModal() {
    openBottomSheet("traveller-selection");
  }

  function getShortClassName(name) {
    if (name === "Business Class") return "Business";
    if (name === "Premium Economy Class") return "Premium";
    if (name === "Economy Class") return "Economy";
    return name; // fallback
  }
</script>

<!-- Class and Travellers -->
<div
  class="w-full flex bg-white border border-gray-200 rounded-lg shadow-sm text-left mb-4"
>
  <div
    class="cursor-pointer flex-1 p-4 px-4"
    on:click|stopPropagation={openModal}
  >
    <div class="flex items-center">
      <div>
        <Icon name="seat-class" size={24} color="#8A8A8A" />
      </div>
      <div class="pl-6">
        <div class="text-gray-500 text-xs">Class</div>
        <div
          class="select-none text-gray-900 font-bold text-lg flex items-center"
        >
          {getShortClassName(selectedClass)}
          <span class="select-none text-gray-500 text-xl ml-1">&#9660;</span>
        </div>
      </div>
    </div>
  </div>

  <div class="pr-8 border-l border-gray-200 my-2" />
  <!-- Vertical divider -->

  <div
    class="cursor-pointer flex-1 p-4 px-4"
    on:click|stopPropagation={openModal}
  >
    <div class="flex items-center">
      <div>
        <Icon name="travellers" size={24} color="#8A8A8A" />
      </div>
      <div class="pl-6">
        <div class="select-none text-gray-500 text-xs">Traveller(s)</div>
        <div
          class="select-none text-gray-900 font-bold text-lg flex items-center"
        >
          {travellers}
          <span class="select-none text-gray-500 text-xl ml-1">&#9660;</span>
        </div>
      </div>
    </div>
  </div>
</div>
