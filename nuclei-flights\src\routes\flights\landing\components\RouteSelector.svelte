<script lang="ts">
  // Import Svelte's event dispatcher to communicate with parent components
  import { createEventDispatcher } from "svelte";
  import Icon from "../../assets/icons/Icon.svelte";

  export let selectedCities;

  // Initialize dispatcher for custom events
  const dispatch = createEventDispatcher();

  // Dispatches a 'click' event with the type ("From" or "To")
  function handleClick(type: any) {
    dispatch("click", type);
  }

  // Dispatches a 'swap' event to parent to swap the departure and destination cities
  function swapCities(event: any) {
    event.stopPropagation();
    dispatch("swap");
  }
</script>

<!-- Flight Input Cards -->
<div class="relative">
  <!-- From Section -->
  <div
    class="cursor-pointer w-full flex items-center justify-between p-4 mb-3 bg-white border border-gray-200 rounded-lg shadow-sm text-left px-4 relative"
    on:click={() => handleClick("From")}
  >
    <div class="flex items-center">
      <div class="flex-shrink-0 mr-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="25"
          viewBox="0 0 24 25"
          fill="none"
        >
          <path
            d="M2 22.5H22"
            stroke="#8A8A8A"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M6.36 17.8991L4 17.4991L2 13.4991L3.1 12.9491C3.37916 12.8085 3.6874 12.7352 4 12.7352C4.3126 12.7352 4.62084 12.8085 4.9 12.9491L5.07 13.0491C5.34916 13.1898 5.6574 13.2631 5.97 13.2631C6.2826 13.2631 6.59084 13.1898 6.87 13.0491L8 12.4991L5 6.49915L5.9 6.04915C6.23267 5.88549 6.60429 5.81753 6.97335 5.85285C7.34242 5.88816 7.6944 6.02537 7.99 6.24915L12.01 9.24915C12.3066 9.47486 12.6604 9.61319 13.0315 9.64853C13.4025 9.68387 13.7761 9.61481 14.11 9.44915L18.3 7.38915C18.8354 7.11924 19.4523 7.05862 20.03 7.21915L21 7.49915C21.2004 7.5548 21.3859 7.65442 21.543 7.79075C21.7001 7.92708 21.8248 8.09671 21.9082 8.28729C21.9915 8.47787 22.0313 8.68463 22.0247 8.89252C22.0181 9.10042 21.9652 9.30423 21.87 9.48915L21.49 10.2491C21.26 10.7091 20.89 11.0891 20.42 11.3291L7.58 17.6991C7.20245 17.8862 6.77547 17.9492 6.36 17.8791V17.8991Z"
            stroke="#8A8A8A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="flex-1">
        <div class="select-none text-gray-500 pl-3 text-xs">From</div>
        <div class="flex items-baseline">
          <span class="select-none text-gray-900 pl-3 font-bold text-lg"
            >{selectedCities.from.city}</span
          >
          <span class="select-none pl-2 text-sm text-gray-600"
            >{selectedCities.from.code}</span
          >
        </div>
        <div class="select-none text-gray-500 pl-3 text-xs">
          {selectedCities.from.airport}
        </div>
      </div>
    </div>

    <!-- Swap Cities Button -->
    <div class="absolute -bottom-8 right-4 z-10">
      <div
        class="cursor-pointer p-1.5 bg-white border-2 border-blue-500 rounded-full shadow-sm hover:bg-gray-50"
        on:click={swapCities}
      >
        <Icon
          name="swap-cities"
          size={24}
          color="#1BA4F7"
          className="w-6 h-6"
        />
      </div>
    </div>
  </div>

  <!-- To Section -->
  <div
    class="cursor-pointer w-full flex items-center justify-between p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm text-left px-4 mt-6"
    on:click={() => handleClick("To")}
  >
    <div class="flex items-center">
      <div class="flex-shrink-0 mr-3">
        <Icon name="flight-to" size={24} color="#8A8A8A" />
      </div>
      <div class="flex-1">
        <div class="select-none text-gray-500 pl-3 text-xs">To</div>
        <div class="flex items-baseline">
          <span class="select-none text-gray-900 pl-3 font-bold text-lg"
            >{selectedCities.to.city}</span
          >
          <span class="select-none pl-2 text-sm text-gray-600"
            >{selectedCities.to.code}</span
          >
        </div>
        <div class="select-none text-gray-500 pl-3 text-xs">
          {selectedCities.to.airport}
        </div>
      </div>
    </div>
  </div>
</div>
