<script lang="ts">
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import SearchBar from "@CDNA-Technologies/svelte-vitals/components/search-bar";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { base } from "$app/paths";

  // props from parent component
  export let heading: string;
  export let searchQuery: string;
  export let isSearching: boolean;
  export let airportsToShow: any[];
  export let recentSearches: any[];
  export let popularCities: any[];
  export let isLoadingPopular: boolean;
  export let handleSearchInput: (event: Event) => void; // Keep for backward compatibility
  export let handleCityClick: (airport: any) => void;
  export let selectedCities: any = null;
  export let isSourceSelection: boolean = false;
  export let showDuplicateError: boolean = false;
  export let duplicateErrorMessage: string = "";

  // New props for SearchBar functionality
  export let onSearchChange: (query: string) => void = () => {};
  export let onSearchClick: () => void = () => {};
  export let onClearSearch: () => void = () => {};

  // Local searchText variable bound to SearchBar
  let searchText = searchQuery;

  // Keep searchText in sync with searchQuery prop
  $: searchText = searchQuery;

  // Handle SearchBar changes and forward to parent
  function handleSearchBarChange(query: string) {
    // Update local searchText
    searchText = query;
    // Call parent's search change handler
    if (onSearchChange) {
      onSearchChange(query);
    }
  }

  // Handle SearchBar click
  function handleSearchBarClick() {
    if (onSearchClick) {
      onSearchClick();
    }
  }

  // Handle clear search
  function handleSearchBarClear() {
    searchText = "";
    if (onClearSearch) {
      onClearSearch();
    }
  }

  function handleBackButton() {
    NavigatorUtils.navigateTo({
      url: base + "/flights/landing",
    });
  }

  $: if (showDuplicateError) {
    setTimeout(() => {
      document.getElementById('duplicate-error')?.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
    }, 100);
  }

  // Determine selection type for placeholder
  $: selectionType = isSourceSelection ? "source" : "destination";
  $: searchPlaceholder = `Enter ${selectionType} city/airport name`;
</script>

<div class="select-none bg-white min-h-screen flex flex-col">
  <AppBar title={heading} onBackButtonClick={handleBackButton} />

  <!-- Replace the input section with SearchBar -->
  <div class="border-b border-gray-200 relative">
    <SearchBar
      bind:searchText
      placeholder={searchPlaceholder}
      onSearchChange={handleSearchBarChange}
      onClick={handleSearchBarClick}
      onCrossIconClick={handleSearchBarClear}
      debounceWaitTime={300}
      minCharacterRequiredForSearch={3}
      maxCharacterLimit={50}
      showCrossIcon={true}
      showSearchIconAhead={false}
      focused={true}
      padding="px-5 py-4"
      inputValidationError="Please enter valid city or airport name"
      selectionError={showDuplicateError ? duplicateErrorMessage : ""}
      validationRegex={/^[a-zA-Z0-9\s\-\.,']*$/}
      textStyle="font-medium text-base"
    />
  </div>

  <!-- Conditional rendering for search results or default lists -->
  {#if searchText}
    <div class="flex-1 overflow-y-auto border-b border-gray-200">
      {#if isSearching}
        <div class="flex justify-center items-center py-8">
          <span class="pl-2 select-none">Searching</span>
          <span class="flex space-x-1">
            <span class="animate-bounce">.</span>
            <span class="animate-bounce [animation-delay:0.2s]">.</span>
            <span class="animate-bounce [animation-delay:0.4s]">.</span>
          </span>
        </div>
      {:else if searchText.length <= 2}
        <div class="flex justify-center items-center py-8">
          <span class="select-none text-gray-500"
            >Enter More to Find Airports</span
          >
        </div>
      {:else if airportsToShow.length > 0}
        {#each airportsToShow as airport}
          <button
            class="w-full flex items-center py-3 px-5 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
            on:click={() => handleCityClick(airport)}
          >
            <div
              class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
            >
              {airport.code}
            </div>
            <div class="flex-1 min-w-0 text-left">
              <div
                class="select-none text-base font-medium text-gray-900 mb-0.5"
              >
                {airport.name}
              </div>
              <div class="select-none text-xs text-gray-500 leading-snug">
                {airport.airport}
              </div>
            </div>
          </button>
        {/each}
      {:else if searchText.length > 2}
        <div class="flex justify-center items-center py-8">
          <span class="select-none text-gray-500">No airports found</span>
        </div>
      {/if}
    </div>
  {:else}
    <!-- Recent Searches Section -->
    {#if recentSearches.length > 0}
      <div class="px-5">
        <div class="flex items-center py-4 pb-3 border-b border-gray-100">
          <svg
            class="text-gray-500 mr-2"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <circle cx="12" cy="12" r="10" />
            <polyline points="12,6 12,12 16,14" />
          </svg>
          <span
            class="select-none text-sm font-medium text-gray-500 uppercase tracking-wider"
          >
            Recent Searches
          </span>
        </div>
        <div class="pb-2">
          {#each recentSearches as airport}
            <button
              class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
              on:click={() => handleCityClick(airport)}
            >
              <div
                class="select-none w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
              >
                {airport.code}
              </div>
              <div class="flex-1 min-w-0 text-left">
                <div
                  class="select-none text-base font-medium text-gray-900 mb-0.5"
                >
                  {airport.name}
                </div>
                <div class="select-none text-xs text-gray-500 leading-snug">
                  {airport.airport}
                </div>
              </div>
            </button>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Popular Cities Section -->
    <div class="px-5">
      <div class="flex items-center py-4 pb-3 border-b border-gray-100">
        <svg
          class="text-gray-500 mr-2"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
          <circle cx="12" cy="10" r="3" />
        </svg>
        <span
          class="select-none text-sm font-medium text-gray-500 uppercase tracking-wider"
        >
          Popular Cities
        </span>
      </div>

      {#if isLoadingPopular}
        <div class="flex justify-center items-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
          />
          <span class="select-none ml-2 text-gray-500"
            >Loading popular cities...</span
          >
        </div>
      {:else}
        <div class="pb-2">
          {#each popularCities as airport}
            <button
              class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
              on:click={() => handleCityClick(airport)}
            >
              <div
                class="select-none w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
              >
                {airport.code}
              </div>
              <div class="flex-1 min-w-0 text-left">
                <div
                  class="select-none text-base font-medium text-gray-900 mb-0.5"
                >
                  {airport.name}
                </div>
                <div class="select-none text-xs text-gray-500 leading-snug">
                  {airport.airport}
                </div>
              </div>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>