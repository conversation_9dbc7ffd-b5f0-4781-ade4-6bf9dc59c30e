<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import type { TransformedFlight } from "$lib/flights-commons/messages/flight-search-params.msg.js";
  import PrimaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";

  export let flights: TransformedFlight[] = [];

  const dispatch = createEventDispatcher();

  function handleFlightSelect(flight: TransformedFlight) {
    dispatch("flightSelect", { flight });
  }
</script>

<!-- Flight List -->
<div class="flex-1 overflow-y-auto pb-4">
  {#if flights.length === 0}
    <div class="flex flex-col items-center justify-center py-8">
      <p class="select-none text-gray-500 text-center">
        No flights found for your search criteria
      </p>
    </div>
  {:else}
    {#each flights as flight}
      <div
        class="bg-white mb-2 mx-4 mt-4 rounded-lg shadow-sm border border-gray-200"
      >
        <!-- Flight Card -->
        <div class="p-4">
          <!-- Airline and Tags -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <div
                class="w-8 h-5 bg-red-600 rounded flex items-center justify-center mr-3"
              >
                <span class="text-white text-xs font-bold">
                  <img
                    src={flight.logo}
                    alt="Airline Logo"
                    width="50"
                    height="50"
                  />
                </span>
              </div>
              <span class="select-none text-gray-700 font-medium"
                >{flight.airline}</span
              >
            </div>
            <div class="flex space-x-2">
              {#each flight.tags as tag}
                <span
                  class="select-none px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded"
                >
                  {tag}
                </span>
              {/each}
            </div>
          </div>

          <!-- Flight Details -->
          <div class="flex items-center justify-between mb-4">
            <!-- Departure -->
            <div class="text-center">
              <div class="select-none text-2xl font-bold text-gray-900">
                {flight.departure}
              </div>
              <div class="select-none text-sm text-gray-500">
                {flight.departureAirport}
              </div>
            </div>

            <!-- Duration -->
            <div class="flex-1 mx-4">
              <div class="text-center">
                <div class="select-none text-xs text-gray-500 mb-1">
                  {flight.duration}
                </div>
                <div class="border-t border-gray-300 relative">
                  <div
                    class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-full h-1/2 bg-green-500"
                  />
                </div>
              </div>
            </div>

            <!-- Arrival -->
            <div class="text-center">
              <div class="select-none text-2xl font-bold text-gray-900">
                {flight.arrival}
              </div>
              <div class="select-none text-sm text-gray-500">
                {flight.arrivalAirport}
              </div>
            </div>

            <!-- Price -->
            <div class="select-none text-right ml-4">
              <div
                class={`text-2xl font-bold ${
                  flight.price < 20000 ? "text-green-600" : "text-black"
                }`}
              >
                ₹ {flight.price.toLocaleString()}
              </div>
              <div class="select-none text-xs text-gray-500">per adult</div>
            </div>
          </div>

          <!-- Offer Banner -->
          {#if flight.offer}
            <div
              class="bg-green-50 border border-green-200 rounded-lg p-2 mb-3"
            >
              <div class="select-none flex items-center text-sm text-green-700">
                <svg
                  class="w-4 h-4 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  />
                </svg>
                {flight.offer}
              </div>
            </div>
          {/if}
        </div>

        <!-- Select Button -->
        <div class="border-t border-gray-200 px-4 py-3">
          <PrimaryButton
            height="h-10"
            designClass="select-none w-full bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 transition-colors"
            on:submit={() => handleFlightSelect(flight)}
          >
            <span class="select-none font-smal">Flight</span>
          </PrimaryButton>
        </div>
      </div>
    {/each}
  {/if}
  <!-- Bottom padding -->
  <div class="h-4" />
</div>
