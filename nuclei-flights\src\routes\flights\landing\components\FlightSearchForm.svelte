<script lang="ts">
  import { base } from "$app/paths";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { createEventDispatcher, onMount } from "svelte";
  import Calendar from "./Calendar.svelte";
  import TravellerClassModal from "./TravellerClassModal.svelte";
  import CitySelector from "./CitySelector.svelte";
  import DateSelector from "./DateSelector.svelte";
  import TravellerSelector from "./TravellerSelector.svelte";
  import SpecialFareSelector from "./SpecialFareSelector.svelte";
  import CompareAndFlyHeader from "./CompareAndFlyHeader.svelte";
  import { dateUtils } from "$lib/flights-commons/utils/date-util";
  import PrimaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";

  export let selectedCities;
  export let searchParams; // New prop to receive stored search parameters

  const dispatch = createEventDispatcher();

  // Initialize with stored values or defaults
  let departureDate = searchParams?.departureDate || "17 Mar";
  let departureDay = searchParams?.departureDay || "Monday";
  let returnDate = searchParams?.returnDate || "";
  let isReturnTrip = searchParams?.isReturnTrip || false;
  let selectedClass = searchParams?.selectedClass || "Business Class";
  let travellers = searchParams?.travellers || "01";
  let showNonStop =
    searchParams?.showNonStop !== undefined ? searchParams.showNonStop : true;
  let selectedSpecialFare = searchParams?.selectedSpecialFare || "Student";
  let adults = searchParams?.adults || 1;
  let children = searchParams?.children || 0;
  let infants = searchParams?.infants || 0;

  let selectedDate: Date | null = null;
  let showCalendar = false;

  function swapCities() {
    const tempCity = selectedCities.from;
    selectedCities.from = selectedCities.to;
    selectedCities.to = tempCity;
    selectedCities = { ...selectedCities };
    dispatch("citySwap", selectedCities);
  }

  function toggleReturnTrip() {
    isReturnTrip = !isReturnTrip;
    if (!isReturnTrip) {
      returnDate = "";
    }

    // Dispatch the update
    dispatchSearchParamsUpdate({
      isReturnTrip,
      returnDate,
    });
  }

  function handleClick(event: any) {
    const type = event.detail;
    console.log("handleClick received type:", type);
    let param;
    if (type === "From") {
      param = "source=true";
      console.log("SOURCE");
    } else if (type === "To") {
      param = "destination=true";
      console.log("DESTINATION");
    } else {
      console.error("Unknown type received:", type);
      param = "destination=true"; // fallback
    }
    console.log(`Navigating to location selection with param: ${param}`);
    NavigatorUtils.navigateTo({
      url: base + `/flights/SearchAirport?${param}`,
      opts: {
        state: {
          selectedCities: selectedCities,
        },
      },
    });
  }

  // Updated handler for BottomSheet
  function handleTravellerSelection(event: any) {
    const {
      adults: newAdults,
      children: newChildren,
      infants: newInfants,
      selectedClass: newClass,
      totalPassengers,
    } = event.detail;

    adults = newAdults;
    children = newChildren;
    infants = newInfants;
    selectedClass = newClass;

    // Format travellers display
    travellers = totalPassengers.toString().padStart(2, "0");

    console.log("Updated traveller selection:", {
      class: selectedClass,
      travellers: travellers,
      breakdown: { adults, children, infants },
      total: totalPassengers,
    });

    // Dispatch the update
    dispatchSearchParamsUpdate({
      selectedClass,
      travellers,
      adults,
      children,
      infants,
    });
  }

  function handleSearchFlights() {
    dispatch("searchFlights", {
      departure: departureDate,
      passengers: parseInt(travellers),
      clas: selectedClass,
      adults,
      children,
      infants,
    });
  }

  function openCalendar() {
    console.log("Opening the Calendar...");
    showCalendar = true;
  }

  function updateDepartureDate(date: Date) {
    selectedDate = date;
    departureDate = dateUtils.formatDate(date);
    departureDay = dateUtils.formatDay(date);

    // Dispatch the update
    dispatchSearchParamsUpdate({
      departureDate,
      departureDay,
    });
  }

  function handleCalendarClose() {
    showCalendar = false;
  }

  function handleCalendarSelect(event: CustomEvent<Date>) {
    const date = event.detail;
    updateDepartureDate(date);
    showCalendar = false;
  }

  function handleSpecialFareChange(event: any) {
    selectedSpecialFare = event.detail;
    dispatchSearchParamsUpdate({
      selectedSpecialFare,
    });
  }

  function handleNonStopChange() {
    dispatchSearchParamsUpdate({
      showNonStop,
    });
  }

  // Helper function to dispatch search params updates
  function dispatchSearchParamsUpdate(updates: any) {
    dispatch("searchParamsUpdate", updates);
  }

  onMount(() => {
    // If we have stored departure date, try to parse it back to a Date object
    if (
      searchParams?.departureDate &&
      searchParams?.departureDate !== "17 Mar"
    ) {
      // Try to reconstruct the date from the stored format
      try {
        // This assumes your dateUtils.formatDate creates a format like "17 Mar"
        // You might need to adjust this based on your actual date format
        const today = new Date();
        selectedDate = today; // Fallback to today if parsing fails
      } catch (e) {
        console.warn("Could not parse stored departure date, using today");
        const today = new Date();
        updateDepartureDate(today);
      }
    } else {
      // Use today as default
      const today = new Date();
      updateDepartureDate(today);
    }
  });
</script>

<div
  class="w-full max-w-lg mx-auto bg-gray-100 p-4 pb-0 rounded-b-lg shadow-md mb-2"
>
  <CompareAndFlyHeader />

  <CitySelector {selectedCities} on:click={handleClick} on:swap={swapCities} />

  <DateSelector
    {departureDate}
    {departureDay}
    {returnDate}
    {isReturnTrip}
    {showCalendar}
    {selectedDate}
    on:openCalendar={openCalendar}
    on:toggleReturn={toggleReturnTrip}
    on:calendarSelect={handleCalendarSelect}
    on:calendarClose={handleCalendarClose}
  >
    <Calendar
      bind:isOpen={showCalendar}
      bind:selectedDate
      position="bottom"
      on:select={handleCalendarSelect}
      on:close={handleCalendarClose}
    />
  </DateSelector>

  <TravellerSelector {selectedClass} {travellers} />

  <SpecialFareSelector
    bind:selectedFare={selectedSpecialFare}
    on:change={handleSpecialFareChange}
  />

  <!-- Show only non-stop flights checkbox -->
  <div class="flex items-center mb-6">
    <input
      type="checkbox"
      id="nonStopFlights"
      class="form-checkbox h-5 w-5 text-sky-600 rounded"
      bind:checked={showNonStop}
      on:change={handleNonStopChange}
    />
    <label for="nonStopFlights" class="ml-2 text-gray-700 text-sm">
      Show only non-stop flights
    </label>
  </div>

  <!-- Search Flights Button -->
  <PrimaryButton
    id="Search Flights"
    height="h-14"
    on:submit={handleSearchFlights}
    designClass="w-full bg-green-500 text-white font-bold py-3 rounded-lg text-lg shadow-md hover:bg-green-600 transition duration-300 m-0"
  >
    <div class="whitespace-nowrap font-['Roboto']">Search Flights</div>
  </PrimaryButton>

  <TravellerClassModal
    modalId="traveller-selection"
    initialAdults={adults}
    initialChildren={children}
    initialInfants={infants}
    initialClass={selectedClass}
    on:done={handleTravellerSelection}
  />
</div>
