import {
    setLoadingLce,
    setContentLce,
    setErrorLce,
    lceStore,
    type LCE
} from '@CDNA-Technologies/svelte-vitals/error-handling';
import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';
import dayjs from 'dayjs';
import type { FlightSearchParams } from '$lib/flights-commons/messages/flight-search-params.msg.ts';
import type { FlightSearchResponse } from '$lib/flights-commons/messages/flight-search-response.msg.ts';
import { transformApiResponse } from '$lib/flights-commons/utils/transform-api-response-util.js';
import { getFlightSearchList } from '$flights/flight-api.js';

export const fetchFlights = async (
    searchParams: FlightSearchParams
): Promise<FlightSearchResponse> => {
    try {
        const { source, destination, departureDate, returnDate, passengers, travellerClass } =
            searchParams;

        setLoadingLce();

        NucleiLogger.logInfo('Starting flight search', 'FLIGHT_SEARCH', {
            from: `${source} (${source.iataCode})`,
            to: `${destination} (${destination.iataCode})`,
            date: departureDate,
            passengers: passengers,
            class: travellerClass.displayName
        });

        // Prepare the API request payload
        const apiPayload = {
            src: {
                iataCode: source.iataCode,
                city: source.city,
                countryCode: source.countryCode || 'IN'
            },
            des: {
                iataCode: destination.iataCode,
                city: destination.city,
                countryCode: destination.countryCode || 'IN',
                fareType: destination.fareType || 'regular',
                partnerCountry: destination.partnerCountry || destination.countryCode || 'IN'
            },
            departDate: dayjs(departureDate).format('DD-MM-YYYY'),
            passenger: {
                adultCount: passengers.adults,
                childCount: passengers.children || 0,
                infantCount: passengers.infants || 0
            },
            travellerClass: {
                key: travellerClass.apiKey,
                value: travellerClass.displayName
            }
        };

        NucleiLogger.logDebug('API payload prepared:', 'API_REQUEST', apiPayload);

        // Make API call
        const response = await getFlightSearchList(apiPayload);

        NucleiLogger.logDebug('API response received:', 'API_RESPONSE', {
            hasError: response.hasError(),
            flightCount: response.response?.onwardFlights?.length || 0
        });

        // If API itself fails, show error state with retry option
        if (response.hasError()) {
            const errorMessage =
                response.error?.description || response.error?.title || 'Flight search failed';

            NucleiLogger.logException('API Error during flight search:', 'API_ERROR', response.error);

            // Set LCE error state with user-friendly message
            setErrorLce({
                title: 'Flight Search Failed',
                description:
                    "We couldn't search for flights right now. Please check your connection and try again.",
                actionText: 'Retry Search',
                barrierDismissible: true
            });

            return {
                flights: [],
                hasError: true,
                error: errorMessage
            };
        }

        // API succeeded but no flights available
        if (!response.response?.onwardFlights || response.response.onwardFlights.length === 0) {
            NucleiLogger.logWarn('No flights found for search criteria', 'FLIGHT_SEARCH');

            // Set LCE error state for "no results" case
            setErrorLce({
                title: 'No Flights Found',
                description: `No flights available from ${source.city} to ${destination.city} on ${dayjs(departureDate).format(
                    'DD MMM YYYY'
                )}. Try different dates or routes.`,
                actionText: 'Modify Search',
                barrierDismissible: true
            });

            return {
                flights: [],
                hasError: true,
                error: 'No flights found for the selected criteria'
            };
        }

        // Transform and set content state
        const transformedFlights = transformApiResponse(response.response, source.iataCode, destination.iataCode);

        if (transformedFlights.length === 0) {
            // Edge case: API returned flights but transformation failed
            NucleiLogger.logWarn('Flight transformation returned empty results', 'FLIGHT_TRANSFORM');

            setErrorLce({
                title: 'Data Processing Error',
                description: "We found flights but couldn't display them properly. Please try again.",
                actionText: 'Retry',
                barrierDismissible: true
            });

            return {
                flights: [],
                hasError: true,
                error: 'Failed to process flight data'
            };
        }

        setContentLce();

        NucleiLogger.logSuccess(`Flight search completed successfully`, 'FLIGHT_SEARCH', {
            flightsFound: transformedFlights.length
        });

        return {
            flights: transformedFlights,
            hasError: false
        };
    } catch (error) {
        // Network errors, code errors, etc.
        NucleiLogger.logException(
            'Unexpected error during flight search:',
            'FLIGHT_SEARCH_ERROR',
            error
        );

        // Set LCE error state for unexpected failures
        setErrorLce({
            title: 'Search Error',
            description:
                'Something went wrong while searching for flights. Please check your internet connection and try again.',
            actionText: 'Try Again',
            barrierDismissible: true
        });

        return {
            flights: [],
            hasError: true,
            error: `Failed to fetch flights: ${error}`
        };
    }
};

export { lceStore };
